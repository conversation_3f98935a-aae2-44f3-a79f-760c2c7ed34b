
package com.jygjexp.jynx.basic.back.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConstructorBinding;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 扫描历史
 *
 * <AUTHOR>
 * @date 2024-10-14 21:11:41
 */
@Data
@TableName("tkzj_zt_scan")
@NoArgsConstructor
@Schema(description = "扫描历史")
public class ScanHistoryEntity extends Model<ScanHistoryEntity> {

    public ScanHistoryEntity(String orderNo, Integer operation, Integer scanFlag, String result) {
        this.orderNo = orderNo;
        this.operation = operation;
        this.scanFlag = scanFlag;
        this.result = result;
        this.time = LocalDateTime.now();
    }

    @ExcelIgnore
    @TableId(type = IdType.AUTO)
    @Schema(description = "主键")
    private Integer id;

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "扫描结果")
    private Integer scanFlag;

    @Schema(description = "创建时间")
    private LocalDateTime time;

    @Schema(description = "备注")
    private String result;

    @Schema(description = "重量")
    private BigDecimal weight;

    @Schema(description = "面单状态")
    private Integer damaged;

    @Schema(description = "长度")
    private BigDecimal length;

    @Schema(description = "宽度")
    private BigDecimal width;

    @Schema(description = "高度")
    private BigDecimal height;

    @Schema(description = "更新结果")
    private Boolean updateFlag;

    @Schema(description = "图片URL")
    private String img;

    @Schema(description = "操作仓库")
    private Integer operation;

    @TableField(exist = false)
    @Schema(description = "是否查询异常订单")
    private Integer exceptionOrder;

    @Schema(description = "相关图片")
    @TableField(exist = false)
    private String damagedUrl;

    @Schema(description = "是否需要检查异常")
    @TableField(exist = false)
    private Integer checkFlag;





}